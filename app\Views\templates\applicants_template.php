<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta name="Description" content="Dakoii Echad Recruitment & Selection System" />
    <link rel="icon" href="<?= base_url() ?>public/assets/system_img/favicon.ico" type="image/x-icon">

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Material Design Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <!-- Font Awesome - Local Installation -->
    <link rel="stylesheet" href="<?= base_url() ?>/public/assets/fontawesome/fontawesome-free-6.4.0-web/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Source Sans Pro - clean, elegant, professional font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+3:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- DataTables Bootstrap 5 -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet">
    <!-- Toastr CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Google Charts Library -->
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>

    <title><?= isset($title) ? $title . ' - ' : '' ?>DERS - Dakoii Echad Recruitment & Selection System</title>

    <style>
        :root {
            /* Primary colors from the PNG flag color scheme */
            --red: #F00F00;             /* Red from PNG flag */
            --red-dark: #D00D00;        /* Darker red for hover states */
            --yellow: #FFC20F;          /* Yellow/Gold from PNG flag */
            --yellow-dark: #E6B00E;     /* Darker yellow for hover states */
            --black: #000000;           /* Black from PNG flag */
            --gray: #BFC1C7;            /* Light gray from the palette */
            --gray-dark: #9FA1A7;       /* Darker gray for hover states */
            --white: #FFFFFF;           /* White from the palette */

            /* UI colors */
            --text-primary: #000000;    /* Primary text color */
            --text-secondary: #333333;  /* Secondary text color */
            --text-light: #FFFFFF;      /* Light text color for dark backgrounds */
            --bg-light: #FFFFFF;        /* Light background */
            --bg-dark: #000000;         /* Dark background */
        }

        /* Optimize font loading behavior */
        @font-face {
            font-family: 'Source Sans 3';
            font-display: swap;
        }

        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            color: var(--text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .navbar {
            background-color: var(--black) !important;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 0.8rem 0;
        }

        .navbar-brand {
            color: var(--white) !important;
            text-decoration: none;
        }

        .navbar-brand:hover {
            color: var(--yellow) !important;
        }

        .navbar-brand img {
            height: 48px;
            width: auto;
        }

        .navbar-toggler {
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 0.4rem 0.6rem;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 194, 15, 0.25);
        }

        .navbar-toggler-icon {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            transition: all 0.3s ease;
            padding: 0.6rem 1rem;
            font-weight: 500;
            position: relative;
        }

        .nav-link:hover {
            color: var(--yellow) !important;
        }

        .nav-link.active {
            color: var(--yellow) !important;
            font-weight: 600;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: var(--red);
            transition: all 0.3s ease;
            transform: translateX(-50%);
            opacity: 0;
        }

        .nav-link:hover::after,
        .nav-link.active::after {
            width: 70%;
            opacity: 1;
        }

        .dropdown-menu {
            border: none;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .dropdown-item:hover {
            background-color: rgba(240, 15, 0, 0.1);
            color: var(--red);
        }

        /* Mobile navigation styles */
        @media (max-width: 991.98px) {
            .navbar-collapse {
                background-color: var(--black);
                margin-top: 1rem;
                padding: 1rem;
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.1);
            }

            .navbar-nav {
                text-align: center;
            }

            .nav-item {
                margin: 0.2rem 0;
            }

            .nav-link {
                padding: 0.8rem 1rem;
                border-radius: 6px;
                margin: 0.1rem 0;
            }

            .nav-link:hover,
            .nav-link.active {
                background-color: rgba(255, 194, 15, 0.1);
            }

            .nav-link::after {
                display: none;
            }

            .navbar-brand span {
                font-size: 1.5rem;
            }

            /* Mobile user info section */
            .d-flex.align-items-center {
                flex-direction: column;
                align-items: center !important;
                margin-top: 1rem;
                padding-top: 1rem;
                border-top: 1px solid rgba(255, 255, 255, 0.1);
            }

            .d-flex.align-items-center > * {
                margin: 0.3rem 0;
            }

            .position-relative.me-3 {
                margin-right: 0 !important;
            }
        }

        /* Ensure proper spacing on mobile */
        @media (max-width: 576px) {
            .navbar-brand span {
                font-size: 1.3rem;
            }

            .navbar-brand img {
                height: 40px !important;
            }

            .container-fluid.px-4 {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }

            .main-content .container-fluid.px-4 {
                padding-left: 1rem !important;
                padding-right: 1rem !important;
            }
        }

        .main-content {
            flex: 1;
            padding: 2rem 0;
        }

        .footer {
            background-color: var(--black) !important;
            color: white;
            padding: 1.5rem 0;
            margin-top: auto;
        }

        /* Card Hover Effect */
        .hover-card {
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            border-radius: 8px;
        }

        .hover-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(240, 15, 0, 0.15);
        }

        /* Notification Badge */
        .notification-badge {
            background-color: var(--red);
        }

        /* Custom Button Styles */
        .btn-primary {
            background-color: var(--red);
            border-color: var(--red);
            color: white;
            font-weight: 500;
            letter-spacing: 0.02em;
            padding: 0.6rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(240, 15, 0, 0.2);
        }

        .btn-primary:hover {
            background-color: var(--red-dark);
            border-color: var(--red-dark);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(240, 15, 0, 0.3);
        }

        .btn-outline-primary {
            color: var(--red);
            border-color: var(--red);
            font-weight: 500;
            letter-spacing: 0.02em;
            padding: 0.6rem 1.5rem;
            transition: all 0.3s ease;
        }

        .btn-outline-primary:hover {
            background-color: var(--red);
            border-color: var(--red);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(240, 15, 0, 0.3);
        }

        /* Additional button styles for consistency */
        .btn-yellow {
            background-color: var(--yellow);
            color: var(--black);
            border: none;
            font-weight: 500;
            letter-spacing: 0.02em;
            padding: 0.6rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(255, 194, 15, 0.2);
        }

        .btn-yellow:hover {
            background-color: var(--yellow-dark);
            color: var(--black);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(255, 194, 15, 0.3);
        }

        .btn-black {
            background-color: var(--black);
            color: white;
            border: none;
            font-weight: 500;
            letter-spacing: 0.02em;
            padding: 0.6rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .btn-black:hover {
            background-color: #333333;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
        }

        /* Form control styles */
        input.form-control, .form-select {
            padding: 0.8rem 1.2rem;
            font-size: 1rem;
            border-radius: 8px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        input.form-control:focus, .form-select:focus {
            border-color: var(--red);
            box-shadow: 0 0 0 3px rgba(240, 15, 0, 0.2);
        }

        /* Utility classes */
        .text-red { color: var(--red) !important; }
        .text-yellow { color: var(--yellow) !important; }
        .text-gray { color: var(--gray) !important; }
        .bg-red { background-color: var(--red) !important; }
        .bg-yellow { background-color: var(--yellow) !important; }
        .bg-gray { background-color: var(--gray) !important; }

        /* Legacy class names for compatibility */
        .text-navy { color: var(--red) !important; }
        .bg-navy { background-color: var(--red) !important; }
        .text-accent-red { color: var(--yellow) !important; }
        .bg-accent-red { background-color: var(--yellow) !important; }
        .text-lime { color: var(--yellow) !important; }

        .hover-text-yellow:hover {
            color: var(--yellow) !important;
            transition: color 0.3s ease;
        }

        /* Headings */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            line-height: 1.3;
            color: var(--red);
        }
    </style>
</head>

<body>
    <!-- Global Page Loading Spinner -->
    <div id="globalPageSpinner" style="position:fixed;z-index:99999;top:0;left:0;width:100vw;height:100vh;background:rgba(255,255,255,0.85);display:flex;align-items:center;justify-content:center;">
        <div class="spinner-border text-danger" style="width:3rem;height:3rem;" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var spinner = document.getElementById('globalPageSpinner');
            if (spinner) spinner.style.display = 'none';
        });
    </script>
    <?php if(session()->get('logged_in')): ?>
    <!-- Applicant Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid px-4">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url('applicant/dashboard') ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="DERS Logo" class="me-3">
                <span class="h4 mb-0">DERS</span>
            </a>

            <!-- Mobile menu toggle button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a href="<?= base_url('applicant/dashboard') ?>" class="nav-link <?= ($menu == 'dashboard') ? 'active' : '' ?>">
                            <i class="fas fa-home me-2"></i>Dashboard
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('applicant/jobs') ?>" class="nav-link <?= ($menu == 'jobs') ? 'active' : '' ?>">
                            <i class="fas fa-briefcase me-2"></i>Job Openings
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('applicant/applications') ?>" class="nav-link <?= ($menu == 'applications') ? 'active' : '' ?>">
                            <i class="fas fa-file-alt me-2"></i>Applications
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('applicant/profile') ?>" class="nav-link <?= ($menu == 'profile') ? 'active' : '' ?>">
                            <i class="fas fa-user me-2"></i>Profile
                        </a>
                    </li>

                    <li class="nav-item">
                        <a href="<?= base_url('applicant/logout') ?>" class="nav-link">
                            <i class="fas fa-sign-out-alt me-2"></i>Logout
                        </a>
                    </li>
                </ul>

                <div class="d-flex align-items-center">
                    <!-- Notifications -->
                    <div class="position-relative me-3">
                        <button class="btn btn-link text-white position-relative" title="Notifications">
                            <i class="fas fa-bell fs-5"></i>
                        </button>
                    </div>

                    <!-- Welcome Text -->
                    <span class="text-white">
                        Welcome, <?= session()->get('applicant_name') ?>
                    </span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container-fluid px-4">
            <?= $this->renderSection('content') ?>
        </div>
    </main>

    <?php else: ?>
    <!-- Public Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="<?= base_url() ?>">
                <img src="<?= base_url() ?>/public/assets/system_img/system-logo.png" alt="DERS Logo" class="me-3">
                <span class="h4 mb-0">DERS</span>
            </a>

            <!-- Mobile menu toggle button -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav"
                    aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a href="<?= base_url() ?>" class="nav-link <?= ($menu == 'home') ? 'active' : '' ?>">
                            <i class="fas fa-home me-2"></i>Home
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('jobs') ?>" class="nav-link <?= ($menu == 'jobs') ? 'active' : '' ?>">
                            <i class="fas fa-briefcase me-2"></i>Jobs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('how-to-apply') ?>" class="nav-link <?= ($menu == 'how_to_apply') ? 'active' : '' ?>">
                            <i class="fas fa-question-circle me-2"></i>How to Apply
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('about') ?>" class="nav-link <?= ($menu == 'about') ? 'active' : '' ?>">
                            <i class="fas fa-info-circle me-2"></i>About
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('applicant/login') ?>" class="nav-link <?= ($menu == 'applicant_login' || $menu == 'register') ? 'active' : '' ?>">
                            <i class="fas fa-user-plus me-2"></i>Apply
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="<?= base_url('login') ?>" class="nav-link <?= ($menu == 'login') ? 'active' : '' ?>">
                            <i class="fas fa-sign-in-alt me-2"></i>Admin
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <?= $this->renderSection('content') ?>
    <?php endif; ?>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row align-items-center gy-4">
                <div class="col-md-4 text-center text-md-start">
                    <a href="https://www.dakoiims.com" target="_blank" class="d-inline-block mb-3">
                        <img src="<?= base_url() ?>/public/assets/system_img/dakoii-systems-logo.png" alt="Dakoii Systems Logo" class="img-fluid" style="max-height: 60px;">
                    </a>
                    <p class="small mb-0 text-white-50">Innovative software solutions for government and enterprise.</p>
                </div>
                <div class="col-md-4 text-center">
                    <h5 class="text-yellow mb-3">Partners in Excellence</h5>
                    <a href="#" class="d-inline-block mb-3">
                        <img src="<?= base_url() ?>/public/assets/system_img/echad-logo.png" alt="Echad Consultancy Services Logo" class="img-fluid" style="max-height: 60px;">
                    </a>
                </div>
                <div class="col-md-4 text-center text-md-end">
                    <h5 class="text-yellow mb-3">Contact</h5>
                    <p class="small mb-1"><i class="fas fa-envelope me-2"></i> <EMAIL></p>
                    <p class="small mb-3"><i class="fas fa-globe me-2"></i> www.dakoiims.com</p>
                    <div class="d-flex justify-content-center justify-content-md-end gap-3">
                        <a href="#" class="text-white hover-text-yellow"><i class="fab fa-facebook fs-5"></i></a>
                        <a href="#" class="text-white hover-text-yellow"><i class="fab fa-twitter fs-5"></i></a>
                        <a href="#" class="text-white hover-text-yellow"><i class="fab fa-linkedin fs-5"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4 border-secondary">
            <div class="row">
                <div class="col-12 text-center">
                    <p class="mb-0 small">
                        &copy; <?= date('Y') ?> Developed by
                        <a href="https://www.dakoiims.com" class="text-yellow text-decoration-none" target="_blank"> <strong>Dakoii Systems</strong> </a>
                        in collaboration with <strong>Echad Consultancy Services</strong>. <span class="align-right">Powered by <?= SYSTEM_NAME ?> <?= SYSTEM_VERSION ?></span>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Core JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <!-- Toastr JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- SweetAlert Messages -->
    <?php if (session()->has('swal_icon')): ?>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            Swal.fire({
                icon: '<?= session()->getFlashdata('swal_icon') ?>',
                title: '<?= session()->getFlashdata('swal_title') ?>',
                text: '<?= session()->getFlashdata('swal_text') ?>',
                confirmButtonColor: '#F00F00'
            });
        });
    </script>
    <?php endif; ?>

    <!-- Toastr Messages -->
    <script>
        // Configure Toastr options
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-top-right",
            "timeOut": "3000"
        };

        <?php if (session()->getFlashdata('success')): ?>
            toastr.success('<?= session()->getFlashdata('success') ?>');
        <?php endif; ?>

        <?php if (session()->getFlashdata('error')): ?>
            toastr.error('<?= session()->getFlashdata('error') ?>');
        <?php endif; ?>
    </script>

    <!-- DERS AI Assistant Chatbot -->
    <?= $this->include('components/chatbot') ?>

    <!-- Page specific scripts -->
    <?= $this->renderSection('scripts') ?>
</body>
</html>