<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
$routes->setAutoRoute(false);
//$autoRoutesImproved(true);
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.


// Disable auto-routing to prevent conflicts
$routes->setAutoRoute(false);

// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
// $routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// Public Routes (No Authentication Required)
$routes->group('', ['priority' => 1], function ($routes) {
    // Home Routes - Using HomeMainController since Home controller doesn't exist
    $routes->get('/', 'HomeMainController::index');
    $routes->get('about', 'HomeMainController::about');
    $routes->get('how-to-apply', 'HomeMainController::howToApply');
    $routes->get('logout', 'HomeAuthController::adminLogout');

    // Public Authentication Routes
    $routes->get('login', 'HomeAuthController::loginForm');
    $routes->post('login', 'HomeAuthController::processLogin');

    // Public Job Routes - Main jobs route accessible without applicant prefix
    $routes->get('jobs', 'HomeJobsController::index');
    $routes->get('jobs/view/(:num)', 'HomeJobsController::view/$1');

    // Public Applicant Routes - only include routes that don't need authentication
    $routes->group('applicant', function ($routes) {
        // Public Authentication Routes
        $routes->get('register', 'HomeAuthController::registerForm');
        $routes->post('register', 'HomeAuthController::processRegister');
        $routes->post('check-email', 'HomeAuthController::checkEmailAvailability');
        $routes->get('activate/(:any)', 'HomeAuthController::activate/$1');
        $routes->get('login', 'HomeAuthController::applicantLoginForm');
        $routes->post('login', 'HomeAuthController::processApplicantLogin');
        $routes->get('logout', 'HomeAuthController::applicantLogout');
        $routes->post('forgot-password', 'HomeAuthController::forgotApplicantPassword');

        // Public Job Routes (duplicated for applicant namespace)
        $routes->get('jobs', 'ApplicantJobsController::index');
        $routes->get('jobs/(:num)', 'ApplicantJobsController::view/$1');
        $routes->get('jobs/position/(:num)', 'ApplicantJobsController::position/$1');
    });

    // Public Dakoii Routes
    $routes->get('dakoii', 'Dakoii::index');
    $routes->get('dakoii/login', 'Dakoii::loginForm');
    $routes->post('dakoii/login', 'Dakoii::processLogin');
    $routes->get('dakoii/logout', 'Dakoii::adminLogout');

    // Public API Routes
    $routes->group('api', function ($routes) {
        $routes->post('get_provinces', 'Api::get_provinces');
        $routes->post('get_countries', 'Api::get_countries');
        $routes->post('get_districts', 'Api::get_districts');
        // $routes->post('get_llgs', 'Api::get_llgs'); // DISABLED - LLG tables not available

        // Chatbot API Route
        $routes->post('chatbot/chat', '\App\Controllers\ChatbotController::chat');
    });
});

// Protected Applicant Routes (Temporarily disabled auth filter for UI development)
// Set a lower priority so these routes are checked after the public routes
$routes->group('applicant', ['priority' => 0], function($routes) {
    // Dashboard & Profile
    $routes->get('dashboard', 'ApplicantController::dashboard');
    $routes->get('profile', 'ApplicantController::profile');
    $routes->get('applications', 'ApplicantJobsController::applications');
    $routes->get('application/(:num)', 'ApplicantApplicationController::viewApplication/$1');

    // Application File Management Routes
    $routes->get('application/(:num)/files/edit', 'ApplicantApplicationController::editFiles/$1');
    $routes->post('application/(:num)/files/upload', 'ApplicantApplicationController::uploadApplicationFile/$1');
    $routes->post('application/(:num)/files/delete/(:num)', 'ApplicantApplicationController::deleteApplicationFile/$1/$2');

    // Job Application Routes
    $routes->get('jobs/submission/(:num)', 'ApplicantJobsController::submissionInterface/$1');
    $routes->post('jobs/process-application/(:num)', 'ApplicantJobsController::processApplication/$1');
    $routes->post('jobs/final-submission/(:num)', 'ApplicantJobsController::finalSubmission/$1');
    $routes->post('jobs/apply/(:num)', 'ApplicantJobsController::apply/$1');
    $routes->get('jobs/download/(:any)', 'ApplicantJobsController::downloadFile/$1');

    // Profile Management Routes
    $routes->group('profile', function($routes) {
        // Basic Profile Updates
        $routes->post('upload-photo', 'ApplicantController::uploadPhoto');
        $routes->post('update-personal', 'ApplicantController::updatePersonal');
        $routes->post('update-documents', 'ApplicantController::updateDocuments');
        $routes->post('update-employment', 'ApplicantController::updateEmployment');
        $routes->post('update-family', 'ApplicantController::updateFamily');
        $routes->post('update-additional', 'ApplicantController::updateAdditional');
        $routes->post('change-password', 'ApplicantController::changePassword');

        // Experience Management
        $routes->post('add-experience', 'ApplicantController::addExperience');
        $routes->post('update-experience', 'ApplicantController::updateExperience');
        $routes->post('delete-experience/(:num)', 'ApplicantController::deleteExperience/$1');

        // Education Management
        $routes->post('add-education', 'ApplicantController::addEducation');
        $routes->post('update-education', 'ApplicantController::updateEducation');
        $routes->post('delete-education/(:num)', 'ApplicantController::deleteEducation/$1');

        // File Management - RESTful approach
        $routes->get('files/create', 'ApplicantController::createFile');
        $routes->post('files/store', 'ApplicantController::uploadFile');
        $routes->get('files/(:num)/edit', 'ApplicantController::editFile/$1');
        $routes->post('files/(:num)/update', 'ApplicantController::updateFile/$1');
        $routes->post('files/(:num)/delete', 'ApplicantController::deleteFile/$1');

        // Legacy routes for backward compatibility
        $routes->post('upload-file', 'ApplicantController::uploadFile');
        $routes->post('update-file', 'ApplicantController::updateFile');
        $routes->post('delete-file/(:num)', 'ApplicantController::deleteFile/$1');
    });

    // Note: File serving route removed since files are now in public directory

    // Application Management
    $routes->get('application/(:num)', 'ApplicantController::viewApplication/$1');
});

// Protected Admin Routes (Temporarily disabled auth filter for UI development)
$routes->group('', ['priority' => 0], function($routes) {
    // Admin Dashboard
    $routes->get('dashboard', 'AdminController::admin_dashboard');
    $routes->get('admin/dashboard', 'AdminController::admin_dashboard');

    // Settings Routes
    $routes->get('settings', 'SettingsController::index'); // Main settings dashboard
    $routes->get('settings/organization', 'OrgSettings::org_settings');
    $routes->post('settings/organization/update', 'OrgSettings::update');

    // Exercise Management
    $routes->group('exercises', function ($routes) {
        $routes->get('/', 'ExercisesController::exercise_management');
        $routes->get('list', 'ExercisesController::list');

        // Debug routes (remove in production)
        $routes->get('debug/(:num)', 'ExercisesController::debug/$1');
        $routes->get('debug', 'ExercisesController::debug');
        $routes->get('test-update/(:num)', 'ExercisesController::testUpdateForm/$1');
        $routes->post('test-update-submit/(:num)', 'ExercisesController::testUpdateSubmit/$1');

        // RESTful CRUD routes
        $routes->get('create', 'ExercisesController::createForm');
        $routes->post('create', 'ExercisesController::create');
        $routes->get('view/(:num)', 'ExercisesController::view/$1');
        $routes->get('edit/(:num)', 'ExercisesController::editForm/$1');
        $routes->post('update/(:num)', 'ExercisesController::update/$1');
        $routes->post('delete/(:num)', 'ExercisesController::delete/$1');
        $routes->post('change-status', 'ExercisesController::changeStatus');

        // Applicant Information and Notice routes
        $routes->get('applicant_information/(:num)', 'ExercisesController::applicantInformationForm/$1');
        $routes->post('applicant_information/(:num)', 'ExercisesController::updateApplicantInformation/$1');
        $routes->get('applicant_notice/(:num)', 'ExercisesController::applicantNoticeForm/$1');
        $routes->post('applicant_notice/(:num)', 'ExercisesController::updateApplicantNotice/$1');

        // Keep for backward compatibility with AJAX calls
        $routes->get('get/(:num)', 'ExercisesController::get/$1');

        // Pre-screening criteria routes
        $routes->get('pre_screen_criteria/(:num)', 'ExercisesController::pre_screen_criteria/$1');
        $routes->post('save_criteria/(:num)', 'ExercisesController::save_criteria/$1');
        $routes->get('get_criteria/(:num)', 'ExercisesController::get_criteria/$1');
    });

    // Position Management
    $routes->group('positions', function($routes) {
        $routes->get('positions_exercises', 'PositionsController::positions_exercises');
        $routes->get('exercises_list', 'PositionsController::exercises_list');
        $routes->get('positions_groups/(:num)', 'PositionsController::positions_groups/$1');
        $routes->post('addPositionGroup', 'PositionsController::addPositionGroup');
        $routes->post('updatePositionGroup', 'PositionsController::updatePositionGroup');
        $routes->post('deletePositionGroup/(:num)', 'PositionsController::deletePositionGroup/$1');
        $routes->get('view_positions/(:num)', 'PositionsController::view_positions/$1');
        $routes->post('add', 'PositionsController::addPosition');
        $routes->post('update', 'PositionsController::updatePosition');
        $routes->post('delete/(:num)', 'PositionsController::deletePosition/$1');
        $routes->get('create', 'PositionsController::create');
        $routes->post('store', 'PositionsController::store');
        $routes->get('edit/(:num)', 'PositionsController::edit/$1');
        $routes->get('show/(:num)', 'PositionsController::show/$1');

        // CSV Import routes
        $routes->get('download-csv-template/(:num)', 'PositionsController::downloadCsvTemplate/$1');
        $routes->post('import-csv', 'PositionsController::importCsv');
    });

    // Application Profiling Routes
    $routes->group('profile_applications_exercise', ['namespace' => 'App\Controllers'], function($routes) {
        $routes->get('/', 'ApplicantProfileController::index'); // List exercises
        $routes->get('exercise/(:num)/positions', 'ApplicantProfileController::exercisePositions/$1'); // List positions for an exercise
        $routes->get('position/(:num)/profile', 'ApplicantProfileController::positionProfile/$1'); // View position profile with applicants
    });

    // Application Pre-Screening Routes - Unified Navigation
    $routes->group('application_pre_screening', ['namespace' => 'App\Controllers'], function($routes) {
        $routes->get('/', 'ApplicationPreScreeningController::exercises'); // Start with exercises list
        $routes->get('show/(:num)', 'ApplicationPreScreeningController::show/$1');
        $routes->post('save/(:num)', 'ApplicationPreScreeningController::save/$1');
        $routes->post('batch_update', 'ApplicationPreScreeningController::batchUpdate');
        $routes->get('exercises', 'ApplicationPreScreeningController::exercises');
        $routes->get('exercise/(:num)/prescreening_applicants', 'ApplicationPreScreeningController::exercisePreScreeningApplicants/$1');
        $routes->get('prescreening_profile/(:num)/(:num)', 'ApplicationPreScreeningController::preScreeningProfile/$1/$2');
        $routes->get('applicant_applications/(:num)/(:num)', 'ApplicationPreScreeningController::applicantApplications/$1/$2');
        $routes->post('save_prescreening_results', 'ApplicationPreScreeningController::save_prescreening_results');
    });
});

// Protected Dakoii Routes (Temporarily disabled auth filter for UI development)
$routes->group('dakoii', function ($routes) {
    // Dashboard
    $routes->get('dashboard', 'Dakoii::dashboard');

    // Exercise Management
    $routes->post('exercise/change-status/(:num)', 'Dakoii::exerciseChangeStatus/$1');

    // Organizations Management
    $routes->group('organization', function ($routes) {
        $routes->get('list', 'Dakoii::organizationList');
        $routes->get('create', 'Dakoii::organizationCreateForm');
        $routes->post('create', 'Dakoii::organizationStore');
        $routes->get('view/(:any)', 'Dakoii::organizationView/$1');
        $routes->get('edit/(:any)', 'Dakoii::organizationEditForm/$1');
        $routes->post('update/(:any)', 'Dakoii::organizationUpdate/$1');

        // License Management
        $routes->get('license/edit/(:any)', 'Dakoii::organizationLicenseEditForm/$1');
        $routes->post('license/update/(:any)', 'Dakoii::organizationLicenseUpdate/$1');

        // Admin Management
        $routes->get('admin/create/(:any)', 'Dakoii::organizationAdminCreateForm/$1');
        $routes->post('admin/create/(:any)', 'Dakoii::organizationAdminStore/$1');
        $routes->get('admin/edit/(:any)/(:num)', 'Dakoii::organizationAdminEditForm/$1/$2');
        $routes->post('admin/update/(:any)', 'Dakoii::organizationAdminUpdate/$1');

        // Exercise Management - View Functions Only (CRUD uses existing ExercisesController)
        $routes->get('exercise/create/(:any)', 'Dakoii::exerciseCreateForm/$1');     // GET /dakoii/organization/exercise/create/{orgcode}
        $routes->get('exercise/view/(:num)', 'Dakoii::exerciseView/$1');            // GET /dakoii/organization/exercise/view/{id}
        $routes->get('exercise/edit/(:num)', 'Dakoii::exerciseEditForm/$1');        // GET /dakoii/organization/exercise/edit/{id}
        $routes->post('exercise/change-status/(:num)', 'Dakoii::exerciseChangeStatus/$1'); // POST /dakoii/organization/exercise/change-status/{id}
    });

    // System Users Management
    $routes->get('system-user/create', 'Dakoii::systemUserCreateForm');
    $routes->post('system-user/create', 'Dakoii::systemUserStore');
    $routes->post('system-user/update', 'Dakoii::systemUserUpdate');

    // Location Management
    $routes->group('', function ($routes) {
        // Province Management - RESTful Routes
        $routes->group('province', function ($routes) {
            $routes->get('list', 'Dakoii::provinceList');                    // GET /dakoii/province/list
            $routes->get('create', 'Dakoii::provinceCreateForm');            // GET /dakoii/province/create
            $routes->post('create', 'Dakoii::provinceCreate');               // POST /dakoii/province/create
            $routes->get('edit/(:num)', 'Dakoii::provinceEditForm/$1');      // GET /dakoii/province/edit/{id}
            $routes->post('update', 'Dakoii::provinceUpdate');               // POST /dakoii/province/update
            $routes->get('delete/(:num)', 'Dakoii::provinceDelete/$1');      // GET /dakoii/province/delete/{id}
            $routes->get('get/(:num)', 'Dakoii::provinceGet/$1');            // GET /dakoii/province/get/{id} (JSON)
        });

        // District Management
        $routes->group('district', function ($routes) {
            $routes->get('list/(:num)', 'Dakoii::districtList/$1');
            $routes->get('get-by-province/(:num)', 'Dakoii::districtGetByProvince/$1');
            $routes->post('create', 'Dakoii::districtCreate');
            $routes->post('update', 'Dakoii::districtUpdate');
            $routes->get('delete/(:num)', 'Dakoii::districtDelete/$1');
        });

        // LLG Management - DISABLED (Tables not available)
        // $routes->group('llg', function ($routes) {
        //     $routes->get('list/(:num)', 'Dakoii::llgList/$1');
        //     $routes->post('create', 'Dakoii::llgCreate');
        //     $routes->post('update', 'Dakoii::llgUpdate');
        //     $routes->get('delete/(:num)', 'Dakoii::llgDelete/$1');
        // });

        // Ward Management - DISABLED (Tables not available)
        // $routes->group('ward', function ($routes) {
        //     $routes->get('list/(:num)', 'Dakoii::wardList/$1');
        //     $routes->post('create', 'Dakoii::wardCreate');
        //     $routes->post('update', 'Dakoii::wardUpdate');
        //     $routes->get('delete/(:num)', 'Dakoii::wardDelete/$1');
        // });

        // Education Management
        $routes->group('education', function ($routes) {
            $routes->post('create', 'Dakoii::educationCreate');
            $routes->post('update', 'Dakoii::educationUpdate');
        });
    });

    // Application Profiling Routes
    $routes->group('profile_applications_exercise', ['namespace' => 'App\Controllers'], function($routes) {
        $routes->get('/', 'ApplicantProfileController::index'); // List exercises
        $routes->get('exercise/(:num)/positions', 'ApplicantProfileController::exercisePositions/$1'); // List positions for an exercise
        $routes->get('position/(:num)/profile', 'ApplicantProfileController::positionProfile/$1'); // View position profile with applicants
    });
});

// Incoming Applications routes
$routes->get('incoming_applications', 'IncomingApplicantsController::index');
$routes->get('incoming_applicants/view/(:num)', 'IncomingApplicantsController::view/$1');
$routes->post('incoming_applicants/acknowledge/(:num)', 'IncomingApplicantsController::acknowledge/$1');
$routes->post('incoming_applicants/batch_acknowledge', 'IncomingApplicantsController::batchAcknowledge');

// Acknowledged Applications routes
$routes->get('acknowledged_applications', 'AcknowledgedApplicationsController::index');
$routes->get('acknowledged_applications/view/(:num)', 'AcknowledgedApplicationsController::view/$1');

// Dakoii Rating Items routes (RESTful)
$routes->get('/dakoii/rating_items/list', 'DakoiiRatingsController::index');
$routes->get('/dakoii/rating_items/new', 'DakoiiRatingsController::new');
$routes->post('/dakoii/rating_items/create', 'DakoiiRatingsController::create');
$routes->get('/dakoii/rating_items/edit/(:num)', 'DakoiiRatingsController::edit/$1');
$routes->post('/dakoii/rating_items/update/(:num)', 'DakoiiRatingsController::update/$1');
$routes->get('/dakoii/rating_items/delete/(:num)', 'DakoiiRatingsController::delete/$1');
$routes->get('/dakoii/rating_items/import', 'DakoiiRatingsController::import');
$routes->get('/dakoii/rating_items/download-template', 'DakoiiRatingsController::downloadTemplate');
$routes->post('/dakoii/rating_items/process-import', 'DakoiiRatingsController::processImport');

// Dakoii Rating Scores routes (RESTful)
$routes->get('/dakoii/rating_scores/new/(:num)', 'DakoiiRatingsController::newScore/$1');
$routes->post('/dakoii/rating_scores/create', 'DakoiiRatingsController::createScore');
$routes->get('/dakoii/rating_scores/edit/(:num)', 'DakoiiRatingsController::editScore/$1');
$routes->post('/dakoii/rating_scores/update/(:num)', 'DakoiiRatingsController::updateScore/$1');
$routes->get('/dakoii/rating_scores/delete/(:num)', 'DakoiiRatingsController::deleteScore/$1');
$routes->get('/dakoii/rating_scores/import/(:num)', 'DakoiiRatingsController::importScores/$1');
$routes->get('/dakoii/rating_scores/download-template/(:num)', 'DakoiiRatingsController::downloadScoresTemplate/$1');
$routes->post('/dakoii/rating_scores/process-import/(:num)', 'DakoiiRatingsController::processScoresImport/$1');

// Rating Routes (RESTful) - Temporarily disabled auth filter for UI development
$routes->group('rating', function($routes) {
    $routes->get('', 'RatingManagementController::index');
    $routes->get('position-groups/(:num)', 'RatingManagementController::positionGroups/$1');
    $routes->get('positions/(:num)', 'RatingManagementController::positions/$1');
    $routes->get('applications/(:num)', 'RatingManagementController::applications/$1');
    $routes->get('rate/(:num)', 'RatingManagementController::rate/$1');
    $routes->post('submit', 'RatingManagementController::submitRating');
    $routes->get('view/(:num)', 'RatingManagementController::viewRating/$1');
    $routes->post('generate_ai_analysis', 'RatingAIController::generate_ai_analysis');
});

// Shortlisting Routes (RESTful) - Temporarily disabled auth filter for UI development
$routes->group('shortlisting', function($routes) {
    $routes->get('', 'ShortListManagementController::index');
    $routes->get('positions/(:num)', 'ShortListManagementController::positions/$1');
    $routes->get('applications/(:num)', 'ShortListManagementController::applications/$1');
    $routes->get('detail/(:num)', 'ShortListManagementController::detail/$1');
    $routes->post('update/(:num)', 'ShortListManagementController::updateStatus/$1');
});

// Interview Management Routes (RESTful) - Temporarily disabled auth filter for UI development
$routes->group('interviews', function($routes) {
    // Interview Sessions Management
    $routes->get('', 'InterviewManagementController::index'); // List exercises with selection status
    $routes->get('sessions/(:num)', 'InterviewManagementController::sessions/$1'); // List interview sessions for exercise
    $routes->get('sessions/create/(:num)', 'InterviewManagementController::createSession/$1'); // Create session form
    $routes->post('sessions/store', 'InterviewManagementController::storeSession'); // Store session
    $routes->get('sessions/edit/(:num)', 'InterviewManagementController::editSession/$1'); // Edit session form
    $routes->post('sessions/update/(:num)', 'InterviewManagementController::updateSession/$1'); // Update session
    $routes->post('sessions/delete/(:num)', 'InterviewManagementController::deleteSession/$1'); // Delete session

    // Interview Session Positions
    $routes->get('session/(:num)/positions', 'InterviewManagementController::sessionPositions/$1'); // List positions in session
    $routes->post('session/(:num)/add-position', 'InterviewManagementController::addPositionToSession/$1'); // Add position to session
    $routes->post('session/(:num)/remove-position/(:num)', 'InterviewManagementController::removePositionFromSession/$1/$2'); // Remove position from session

    // Interview Settings
    $routes->get('session/(:num)/settings', 'InterviewManagementController::sessionSettings/$1'); // Interview settings form
    $routes->post('session/(:num)/settings/save', 'InterviewManagementController::saveSessionSettings/$1'); // Save settings

    // Interview Schedule
    $routes->get('session/(:num)/schedule', 'InterviewManagementController::sessionSchedule/$1'); // Interview schedule
    $routes->post('session/(:num)/schedule/generate', 'InterviewManagementController::generateSchedule/$1'); // Generate schedule
    $routes->post('session/(:num)/schedule/update', 'InterviewManagementController::updateSchedule/$1'); // Update schedule

    // Interview Session Interviewers
    $routes->get('session/(:num)/interviewers', 'InterviewManagementController::sessionInterviewers/$1'); // List interviewers in session
    $routes->post('session/(:num)/add-interviewer', 'InterviewManagementController::addInterviewerToSession/$1'); // Add interviewer to session
    $routes->post('session/(:num)/remove-interviewer/(:num)', 'InterviewManagementController::removeInterviewerFromSession/$1/$2'); // Remove interviewer from session
    $routes->post('session/(:num)/update-interviewer/(:num)', 'InterviewManagementController::updateSessionInterviewer/$1/$2'); // Update interviewer role

    // Position Interview Management
    $routes->get('position/(:num)/questions', 'InterviewManagementController::positionQuestions/$1'); // Manage questions
    $routes->post('position/(:num)/questions/save', 'InterviewManagementController::saveQuestions/$1'); // Save questions
    $routes->get('position/(:num)/interviewees', 'InterviewManagementController::positionInterviewees/$1'); // List interviewees
    $routes->get('position/(:num)/scoring', 'InterviewManagementController::positionScoring/$1'); // Interview scoring
    $routes->post('position/(:num)/scoring/save', 'InterviewManagementController::saveScoring/$1'); // Save scores
});

// Reports Routes (RESTful) - Temporarily disabled auth filter for UI development
$routes->group('reports', function($routes) {
    // Main Reports Dashboard
    $routes->get('', 'ReportsMainController::index'); // List exercises with selection status
    $routes->get('exercises', 'ReportsMainController::exercises'); // List exercises for reports
    $routes->get('dashboard/(:num)', 'ReportsMainController::dashboard/$1'); // Reports dashboard for exercise
    $routes->get('positions/(:num)', 'ReportsMainController::positions/$1'); // List positions for exercise
    $routes->get('positions-report/(:num)', 'ReportsMainController::positionsReport/$1'); // Positions report for exercise

    // Application Reports
    $routes->get('application-register/(:num)', 'ReportsApplicationController::applicationRegister/$1'); // Application register report
    $routes->get('application-register-position/(:num)', 'ReportsApplicationController::applicationRegisterByPosition/$1'); // Application register by position
    $routes->get('pre-screening/(:num)', 'ReportsApplicationController::preScreening/$1'); // Pre-screening report
    $routes->get('positions-with-applications/(:num)', 'ReportsApplicationController::positionsWithApplications/$1'); // Positions with applications
    $routes->get('positions-without-applications/(:num)', 'ReportsApplicationController::positionsWithoutApplications/$1'); // Positions without applications
    $routes->post('application-register/export', 'ReportsApplicationController::exportApplicationRegister'); // Export application register
    $routes->post('pre-screening/export', 'ReportsApplicationController::exportPreScreening'); // Export pre-screening

    // Profiling Reports (Form 3.7 and 3.7A)
    $routes->get('profiling/(:num)', 'ReportsProfilingController::profiling/$1'); // Form 3.7 profiling report
    $routes->get('profiling-updated/(:num)', 'ReportsProfilingController::profilingUpdated/$1'); // Form 3.7A updated report
    $routes->post('profiling/export', 'ReportsProfilingController::exportProfiling'); // Export profiling report
    $routes->post('profiling-updated/export', 'ReportsProfilingController::exportProfilingUpdated'); // Export updated report

    // Scoring Reports
    $routes->get('scoring/(:num)', 'ReportsScoringController::scoring/$1'); // Scoring report for position
    $routes->get('interview-scoring/(:num)', 'ReportsScoringController::interviewScoring/$1'); // Interview scoring report
    $routes->post('scoring/export', 'ReportsScoringController::exportScoring'); // Export scoring report
    $routes->post('interview-scoring/export', 'ReportsScoringController::exportInterviewScoring'); // Export interview scoring
});

/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
