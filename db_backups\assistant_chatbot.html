<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DERS Assistant Chatbot</title>
    <!-- Marked.js for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        /* Chatbot Container */
        .chatbot-container {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 350px;
            height: 500px;
            background: #ffffff;
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            display: flex;
            flex-direction: column;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            transition: all 0.3s ease;
            border: 1px solid #e0e0e0;
        }

        .chatbot-container.minimized {
            height: 60px;
            overflow: hidden;
        }

        /* Header */
        .chatbot-header {
            background: linear-gradient(135deg, #2e7d32, #4caf50);
            color: white;
            padding: 15px 20px;
            border-radius: 15px 15px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .chatbot-title {
            font-weight: 600;
            font-size: 16px;
        }

        .chatbot-subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .minimize-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 5px;
            border-radius: 3px;
            transition: background 0.2s;
        }

        .minimize-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* Chat Messages */
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 80%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }

        .message.bot .message-content {
            background: #e8f5e8;
            color: #2e7d32;
            border-bottom-left-radius: 6px;
        }

        .message.user .message-content {
            background: #2e7d32;
            color: white;
            border-bottom-right-radius: 6px;
        }

        .message.system .message-content {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            text-align: center;
            font-size: 12px;
        }

        /* HTML formatting styles for bot messages */
        .message.bot .message-content strong {
            font-weight: 600;
            color: #1b5e20;
        }

        .message.bot .message-content em {
            font-style: italic;
            color: #2e7d32;
        }

        .message.bot .message-content ul {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message.bot .message-content li {
            margin: 4px 0;
            line-height: 1.4;
        }

        .message.bot .message-content p {
            margin: 8px 0;
            line-height: 1.4;
        }

        .message.bot .message-content br {
            line-height: 1.6;
        }

        /* Input Area */
        .chat-input-area {
            padding: 15px 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
            border-radius: 0 0 15px 15px;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s;
        }

        .chat-input:focus {
            border-color: #4caf50;
        }

        .send-btn {
            background: #4caf50;
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 50%;
            cursor: pointer;
            transition: background 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .send-btn:hover {
            background: #45a049;
        }

        .send-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* Loading Animation */
        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: #e8f5e8;
            border-radius: 18px;
            border-bottom-left-radius: 6px;
            max-width: 80%;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dots span {
            width: 8px;
            height: 8px;
            background: #4caf50;
            border-radius: 50%;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-dots span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 60%, 100% {
                transform: translateY(0);
                opacity: 0.5;
            }
            30% {
                transform: translateY(-10px);
                opacity: 1;
            }
        }

        /* Scrollbar Styling */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* Mobile Responsiveness */
        @media (max-width: 480px) {
            .chatbot-container {
                width: calc(100vw - 40px);
                height: calc(100vh - 40px);
                bottom: 20px;
                right: 20px;
                left: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="chatbot-container" id="chatbot">
        <div class="chatbot-header" onclick="toggleChatbot()">
            <div>
                <div class="chatbot-title">DERS Assistant</div>
                <div class="chatbot-subtitle">AI-Powered Help</div>
            </div>
            <button class="minimize-btn" id="minimizeBtn">−</button>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="message-content">
                    👋 Hello! I'm your DERS Assistant. I can help you with questions about the Dakoii Echad Recruitment & Selection System. Ask me anything about registration, profile completion, job applications, or system features!
                </div>
            </div>
        </div>
        
        <div class="typing-indicator" id="typingIndicator">
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
        
        <div class="chat-input-area">
            <div class="input-container">
                <input type="text" class="chat-input" id="chatInput" placeholder="Ask me about DERS..." maxlength="500">
                <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        // Configuration
        const GEMINI_API_KEY = 'AIzaSyApNBtEwylsW_q5zUOvIDP3pj87WDShSLA';
        const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent';
        
        // DERS Context - This is the knowledge base for the chatbot
        const DERS_CONTEXT = `You are a helpful assistant for the DERS (Dakoii Echad Recruitment & Selection System).

DERS is Papua New Guinea's first AI-integrated recruitment and selection system. Here's the complete information you should use to answer user questions:

SYSTEM OVERVIEW:
- AI-Powered Document Analysis with automatic text extraction
- Comprehensive Profile Management
- Real-time Application Tracking
- Secure File Upload with AI processing
- Email Notifications for status updates

GETTING STARTED:
Users need: valid email, personal info, employment history, educational qualifications, digital documents (CV, certificates), referee contacts, and ID documents.

REGISTRATION PROCESS:
1. Visit DERS homepage → Click "Apply" → "Create Account"
2. Fill required info: First Name, Last Name, Email, Password (min 4 chars)
3. Submit registration
4. Check email for activation <NAME_EMAIL>

ACCOUNT ACTIVATION:
- Click activation link in email
- Check spam folder if not received
- If activation link expired: Use "Forgot Password" feature to automatically activate account and receive new password

LOGIN:
- Go to homepage → "Apply" → "Login"
- Enter email and password
- Account must be activated first

PROFILE COMPLETION (MANDATORY - 100% required before applying):
1. Personal Information: Full name, DOB, gender, marital status, contact info, address, origin, citizenship
2. Employment Information: Current employer, position, salary, public servant status, file number
3. Identification: ID numbers, ID photo (JPG/JPEG/PNG, max 2MB)
4. Additional: Referees (min 2), children info, criminal convictions

FILE UPLOAD:
- Supported: PDF (documents only), JPG, JPEG, PNG (images)
- Max size: 25MB per file
- AI processing: Automatic text extraction using Powerful Advanced AI
- Large files (20+ pages) may take 5+ minutes

JOB APPLICATIONS:
Prerequisites: 100% complete profile + at least 1 uploaded file
Process: Browse positions → Select job → Review details → Apply → AI analysis → Submit
Statuses: Pending, Submitted, Active, Shortlisted, Interviewed, Selected, Rejected

PASSWORD RECOVERY:
- Click "Forgot Password" on login page
- Enter registered email
- Receive 4-digit password via email
- Login and change password

TECHNICAL REQUIREMENTS:
- Modern browsers (Chrome, Firefox, Safari, Edge)
- Stable internet connection
- Valid email address

CONTACT SUPPORT:
- Email: <EMAIL>
- Website: www.dakoiims.com
- System emails from: <EMAIL>

Always provide helpful, accurate information based on this context. If asked about something not covered, suggest contacting <NAME_EMAIL>.`;

        let isMinimized = false;
        let conversationHistory = [];

        // Initialize chatbot
        document.addEventListener('DOMContentLoaded', function() {
            const chatInput = document.getElementById('chatInput');
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
        });

        function toggleChatbot() {
            const chatbot = document.getElementById('chatbot');
            const minimizeBtn = document.getElementById('minimizeBtn');
            
            isMinimized = !isMinimized;
            
            if (isMinimized) {
                chatbot.classList.add('minimized');
                minimizeBtn.textContent = '+';
            } else {
                chatbot.classList.remove('minimized');
                minimizeBtn.textContent = '−';
            }
        }

        async function sendMessage() {
            const chatInput = document.getElementById('chatInput');
            const message = chatInput.value.trim();
            
            if (!message) return;
            
            // Clear input
            chatInput.value = '';
            
            // Add user message to chat
            addMessage(message, 'user');
            
            // Show typing indicator
            showTypingIndicator();
            
            // Disable send button
            const sendBtn = document.getElementById('sendBtn');
            sendBtn.disabled = true;
            
            try {
                // Get AI response
                const response = await getGeminiResponse(message);
                
                // Hide typing indicator
                hideTypingIndicator();
                
                // Add bot response
                addMessage(response, 'bot');
                
            } catch (error) {
                console.error('Error getting AI response:', error);
                hideTypingIndicator();
                addMessage('Sorry, I encountered an error. Please try again or contact <NAME_EMAIL>', 'bot');
            }
            
            // Re-enable send button
            sendBtn.disabled = false;
        }

        async function getGeminiResponse(userMessage) {
            // Add user message to conversation history
            conversationHistory.push({
                role: 'user',
                content: userMessage
            });
            
            // Prepare the prompt with context
            const prompt = `You are a DERS (Dakoii Echad Recruitment & Selection System) assistant. You must ALWAYS consult and reference the following DERS information first before responding to any user question:

${DERS_CONTEXT}

Previous conversation:
${conversationHistory.slice(-6).map(msg => `${msg.role}: ${msg.content}`).join('\n')}

User question: ${userMessage}

CRITICAL INSTRUCTIONS:
1. ALWAYS check the DERS information above first for relevant details
2. Base your response strictly on the provided DERS context
3. Provide precise, accurate answers using only the information from the DERS system guide
4. If the question is not covered in the DERS information, clearly state this and suggest contacting <NAME_EMAIL>
5. Keep responses concise but complete
6. Do not speculate or provide information not found in the DERS context
7. If the question is not related to DERS, politely redirect to DERS-related topics
8. ABSOLUTELY NEVER mention ANY specific AI model names (Gemini, GPT, Claude, ChatGPT, OpenAI, etc.) - ALWAYS refer to AI technology ONLY as "Powerful Advanced AI"
9. If asked specifically about what AI model or technology DERS uses, respond: "DERS uses Powerful Advanced AI for document processing and analysis."
10. FORMAT ALL RESPONSES IN HTML - Use proper HTML tags like <strong>, <em>, <ul>, <li>, <br>, <p> etc. for formatting instead of markdown. This ensures proper display in the chat interface.

Respond based strictly on the DERS information provided above. Remember: NO SPECIFIC AI MODEL NAMES EVER! Use HTML formatting for all responses.`;

            const requestBody = {
                contents: [{
                    parts: [{
                        text: prompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 1024,
                }
            };

            const response = await fetch(`${GEMINI_API_URL}?key=${GEMINI_API_KEY}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            const aiResponse = data.candidates[0].content.parts[0].text;
            
            // Add AI response to conversation history
            conversationHistory.push({
                role: 'assistant',
                content: aiResponse
            });
            
            // Keep only last 10 messages to manage memory
            if (conversationHistory.length > 10) {
                conversationHistory = conversationHistory.slice(-10);
            }
            
            return aiResponse;
        }

        function addMessage(message, sender) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';

            // For bot messages, use innerHTML to render HTML formatting
            // For user messages, use textContent for security
            if (sender === 'bot') {
                messageContent.innerHTML = message;
            } else {
                messageContent.textContent = message;
            }

            messageDiv.appendChild(messageContent);
            chatMessages.appendChild(messageDiv);

            // Scroll to bottom
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function showTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            const chatMessages = document.getElementById('chatMessages');
            
            typingIndicator.style.display = 'block';
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            typingIndicator.style.display = 'none';
        }
    </script>
</body>
</html>
