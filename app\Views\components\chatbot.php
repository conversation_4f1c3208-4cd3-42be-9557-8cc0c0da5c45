<?php
// Read the context file and prepare it for JavaScript
$contextFile = ROOTPATH . 'db_backups/applicants_info.md';
$contextContent = '';

if (file_exists($contextFile)) {
    $contextContent = file_get_contents($contextFile);
    // Convert markdown to plain text for AI context
    $contextContent = strip_tags($contextContent);
    // Escape for JavaScript
    $contextContent = addslashes($contextContent);
} else {
    // Fallback context if file doesn't exist
    $contextContent = 'DERS (Dakoii Echad Recruitment & Selection System) is Papua New Guinea\'s first AI-integrated recruitment and selection system. For support, contact <EMAIL>';
}
?>

<!-- DERS AI Assistant Chatbot -->
<div class="ders-chatbot-container minimized" id="dersChatbot">
    <div class="ders-chatbot-header" onclick="toggleDersChatbot()">
        <div>
            <div class="ders-chatbot-title">DERS Assistant</div>
            <div class="ders-chatbot-subtitle">AI-Powered Help</div>
        </div>
        <button class="ders-minimize-btn" id="dersMinimizeBtn">+</button>
    </div>
    
    <div class="ders-chat-messages" id="dersChatMessages">
        <div class="ders-message ders-bot">
            <div class="ders-message-content">
                👋 Hello! I'm your DERS Assistant. I can help you with questions about the Dakoii Echad Recruitment & Selection System. Ask me anything about registration, profile completion, job applications, or system features!
            </div>
        </div>
    </div>
    
    <div class="ders-typing-indicator" id="dersTypingIndicator">
        <div class="ders-typing-dots">
            <span></span>
            <span></span>
            <span></span>
        </div>
    </div>
    
    <div class="ders-chat-input-area">
        <div class="ders-input-container">
            <input type="text" class="ders-chat-input" id="dersChatInput" placeholder="Ask me about DERS..." maxlength="500">
            <button class="ders-send-btn" id="dersSendBtn" onclick="sendDersMessage()">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                </svg>
            </button>
        </div>
    </div>
</div>

<!-- Marked.js for Markdown rendering -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>

<style>
/* DERS Chatbot Styles */
.ders-chatbot-container {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 350px;
    height: 500px;
    background: #ffffff;
    border-radius: 15px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: all 0.3s ease;
    border: 1px solid #e0e0e0;
}

.ders-chatbot-container.minimized {
    height: 60px;
    overflow: hidden;
}

/* Header */
.ders-chatbot-header {
    background: linear-gradient(135deg, #2e7d32, #4caf50);
    color: white;
    padding: 15px 20px;
    border-radius: 15px 15px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.ders-chatbot-title {
    font-weight: 600;
    font-size: 16px;
}

.ders-chatbot-subtitle {
    font-size: 12px;
    opacity: 0.9;
}

.ders-minimize-btn {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: background 0.2s;
}

.ders-minimize-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Chat Messages */
.ders-chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
}

.ders-message {
    margin-bottom: 15px;
    display: flex;
    align-items: flex-start;
}

.ders-message.ders-user {
    justify-content: flex-end;
}

.ders-message-content {
    max-width: 80%;
    padding: 12px 16px;
    border-radius: 18px;
    font-size: 14px;
    line-height: 1.4;
}

.ders-message.ders-bot .ders-message-content {
    background: #e8f5e8;
    color: #2e7d32;
    border-bottom-left-radius: 6px;
}

.ders-message.ders-user .ders-message-content {
    background: #2e7d32;
    color: white;
    border-bottom-right-radius: 6px;
}

.ders-message.ders-system .ders-message-content {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
    text-align: center;
    font-size: 12px;
}

/* HTML formatting styles for bot messages */
.ders-message.ders-bot .ders-message-content strong {
    font-weight: 600;
    color: #1b5e20;
}

.ders-message.ders-bot .ders-message-content em {
    font-style: italic;
    color: #2e7d32;
}

.ders-message.ders-bot .ders-message-content ul {
    margin: 8px 0;
    padding-left: 20px;
}

.ders-message.ders-bot .ders-message-content li {
    margin: 4px 0;
    line-height: 1.4;
}

.ders-message.ders-bot .ders-message-content p {
    margin: 8px 0;
    line-height: 1.4;
}

.ders-message.ders-bot .ders-message-content br {
    line-height: 1.6;
}

/* Input Area */
.ders-chat-input-area {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    background: white;
    border-radius: 0 0 15px 15px;
}

.ders-input-container {
    display: flex;
    gap: 10px;
    align-items: center;
}

.ders-chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    outline: none;
    transition: border-color 0.2s;
}

.ders-chat-input:focus {
    border-color: #4caf50;
}

.ders-send-btn {
    background: #4caf50;
    color: white;
    border: none;
    padding: 12px 16px;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ders-send-btn:hover {
    background: #45a049;
}

.ders-send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Loading Animation */
.ders-typing-indicator {
    display: none;
    padding: 12px 16px;
    background: #e8f5e8;
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    max-width: 80%;
}

.ders-typing-dots {
    display: flex;
    gap: 4px;
}

.ders-typing-dots span {
    width: 8px;
    height: 8px;
    background: #4caf50;
    border-radius: 50%;
    animation: ders-typing 1.4s infinite ease-in-out;
}

.ders-typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.ders-typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes ders-typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.5;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

/* Scrollbar Styling */
.ders-chat-messages::-webkit-scrollbar {
    width: 6px;
}

.ders-chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.ders-chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.ders-chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Mobile Responsiveness */
@media (max-width: 480px) {
    .ders-chatbot-container {
        width: calc(100vw - 40px);
        height: calc(100vh - 40px);
        bottom: 20px;
        right: 20px;
        left: 20px;
    }
}
</style>

<script>
// DERS Chatbot Configuration
const DERS_CHATBOT_API_URL = '<?= base_url('api/chatbot/chat') ?>';

let dersIsMinimized = true; // Start minimized by default
let dersConversationHistory = [];

// Initialize DERS chatbot
document.addEventListener('DOMContentLoaded', function() {
    const dersChatInput = document.getElementById('dersChatInput');
    if (dersChatInput) {
        dersChatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendDersMessage();
            }
        });
    }

});

function toggleDersChatbot() {
    const chatbot = document.getElementById('dersChatbot');
    const minimizeBtn = document.getElementById('dersMinimizeBtn');
    
    if (!chatbot || !minimizeBtn) return;
    
    dersIsMinimized = !dersIsMinimized;
    
    if (dersIsMinimized) {
        chatbot.classList.add('minimized');
        minimizeBtn.textContent = '+';
    } else {
        chatbot.classList.remove('minimized');
        minimizeBtn.textContent = '−';
    }
}

async function sendDersMessage() {
    const chatInput = document.getElementById('dersChatInput');
    const message = chatInput ? chatInput.value.trim() : '';
    
    if (!message) return;
    
    // Clear input
    if (chatInput) chatInput.value = '';
    
    // Add user message to chat
    addDersMessage(message, 'ders-user');
    
    // Show typing indicator
    showDersTypingIndicator();
    
    // Disable send button
    const sendBtn = document.getElementById('dersSendBtn');
    if (sendBtn) sendBtn.disabled = true;
    
    try {
        // Get AI response
        const response = await getDersAIResponse(message);

        // Hide typing indicator
        hideDersTypingIndicator();

        // Add bot response
        addDersMessage(response, 'ders-bot');

    } catch (error) {
        console.error('Error getting AI response:', error);
        hideDersTypingIndicator();
        addDersMessage('Sorry, I encountered an error. Please try again or contact <NAME_EMAIL>', 'ders-bot');
    }
    
    // Re-enable send button
    if (sendBtn) sendBtn.disabled = false;
}

async function getDersAIResponse(userMessage) {
    // Add user message to conversation history
    dersConversationHistory.push({
        role: 'user',
        content: userMessage
    });

    // Prepare request data
    const requestData = {
        message: userMessage,
        history: dersConversationHistory.slice(-6) // Send last 6 messages for context
    };

    const response = await fetch(DERS_CHATBOT_API_URL, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    });

    if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.success) {
        throw new Error(data.message || 'Unknown error occurred');
    }

    const aiResponse = data.response;

    // Add AI response to conversation history
    dersConversationHistory.push({
        role: 'assistant',
        content: aiResponse
    });

    // Keep only last 10 messages to manage memory
    if (dersConversationHistory.length > 10) {
        dersConversationHistory = dersConversationHistory.slice(-10);
    }

    return aiResponse;
}

function addDersMessage(message, sender) {
    const chatMessages = document.getElementById('dersChatMessages');
    if (!chatMessages) return;

    const messageDiv = document.createElement('div');
    messageDiv.className = `ders-message ${sender}`;

    const messageContent = document.createElement('div');
    messageContent.className = 'ders-message-content';

    // For bot messages, use innerHTML to render HTML formatting
    // For user messages, use textContent for security
    if (sender === 'ders-bot') {
        messageContent.innerHTML = message;
    } else {
        messageContent.textContent = message;
    }

    messageDiv.appendChild(messageContent);
    chatMessages.appendChild(messageDiv);

    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function showDersTypingIndicator() {
    const typingIndicator = document.getElementById('dersTypingIndicator');
    const chatMessages = document.getElementById('dersChatMessages');

    if (typingIndicator && chatMessages) {
        typingIndicator.style.display = 'block';
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
}

function hideDersTypingIndicator() {
    const typingIndicator = document.getElementById('dersTypingIndicator');
    if (typingIndicator) {
        typingIndicator.style.display = 'none';
    }
}
</script>
